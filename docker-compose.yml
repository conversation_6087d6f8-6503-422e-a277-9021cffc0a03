version: '3.8'

services:
  trellis:
    build:
      context: .
      dockerfile: Dockerfile
    image: trellis:latest
    container_name: trellis-app
    ports:
      - "7860:7860"
    volumes:
      # Mount the current directory to /app for development
      - .:/app
      # Mount a volume for model cache
      - trellis-cache:/root/.cache
      # Mount a volume for outputs
      - ./outputs:/app/outputs
      # Mount a volume for temporary files
      - ./tmp:/app/tmp
    environment:
      - NVIDIA_VISIBLE_DEVICES=all
      - ATTN_BACKEND=flash-attn
      - SPCONV_ALGO=native
      - CUDA_VISIBLE_DEVICES=0
    runtime: nvidia
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    stdin_open: true
    tty: true
    restart: unless-stopped
    command: ["conda", "run", "-n", "trellis", "python", "app.py", "--server-name", "0.0.0.0", "--server-port", "7860"]

  # Alternative service for text-to-3D demo
  trellis-text:
    build:
      context: .
      dockerfile: Dockerfile
    image: trellis:latest
    container_name: trellis-text-app
    ports:
      - "7861:7860"
    volumes:
      - .:/app
      - trellis-cache:/root/.cache
      - ./outputs:/app/outputs
      - ./tmp:/app/tmp
    environment:
      - NVIDIA_VISIBLE_DEVICES=all
      - ATTN_BACKEND=flash-attn
      - SPCONV_ALGO=native
      - CUDA_VISIBLE_DEVICES=0
    runtime: nvidia
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    stdin_open: true
    tty: true
    restart: unless-stopped
    command: ["conda", "run", "-n", "trellis", "python", "app_text.py", "--server-name", "0.0.0.0", "--server-port", "7860"]
    profiles:
      - text

  # Service for running examples
  trellis-example:
    build:
      context: .
      dockerfile: Dockerfile
    image: trellis:latest
    container_name: trellis-example
    volumes:
      - .:/app
      - trellis-cache:/root/.cache
      - ./outputs:/app/outputs
    environment:
      - NVIDIA_VISIBLE_DEVICES=all
      - ATTN_BACKEND=flash-attn
      - SPCONV_ALGO=native
      - CUDA_VISIBLE_DEVICES=0
    runtime: nvidia
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    stdin_open: true
    tty: true
    command: ["conda", "run", "-n", "trellis", "python", "example.py"]
    profiles:
      - example

volumes:
  trellis-cache:
    driver: local
