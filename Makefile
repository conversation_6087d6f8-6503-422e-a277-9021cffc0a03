# TRELLIS Docker Makefile
# Provides convenient commands for Docker operations

.PHONY: help build run run-text example shell stop clean logs health check-docker

# Default target
help:
	@echo "TRELLIS Docker Commands:"
	@echo "  make build      - Build the Docker image"
	@echo "  make run        - Run the main demo (image-to-3D)"
	@echo "  make run-text   - Run the text-to-3D demo"
	@echo "  make example    - Run the example script"
	@echo "  make shell      - Open interactive shell"
	@echo "  make stop       - Stop all containers"
	@echo "  make clean      - Clean up containers and images"
	@echo "  make logs       - Show container logs"
	@echo "  make health     - Run health check"
	@echo "  make check-docker - Check Docker setup"

# Build the Docker image
build:
	@echo "Building TRELLIS Docker image..."
	docker-compose build

# Run the main demo
run:
	@echo "Starting TRELLIS demo..."
	@mkdir -p outputs tmp
	docker-compose up trellis

# Run the text-to-3D demo
run-text:
	@echo "Starting TRELLIS text-to-3D demo..."
	@mkdir -p outputs tmp
	docker-compose --profile text up trellis-text

# Run example
example:
	@echo "Running TRELLIS example..."
	@mkdir -p outputs
	docker-compose --profile example up trellis-example

# Open interactive shell
shell:
	@echo "Opening interactive shell..."
	docker run -it --rm --gpus all \
		-v "$$(pwd):/app" \
		-v "$$(pwd)/outputs:/app/outputs" \
		-e CUDA_VISIBLE_DEVICES=0 \
		-e ATTN_BACKEND=flash-attn \
		-e SPCONV_ALGO=native \
		trellis:latest \
		conda run -n trellis bash

# Stop all containers
stop:
	@echo "Stopping all containers..."
	docker-compose down

# Clean up
clean:
	@echo "Cleaning up containers and images..."
	docker-compose down --rmi all --volumes --remove-orphans

# Show logs
logs:
	@echo "Showing container logs..."
	docker-compose logs -f

# Run health check
health:
	@echo "Running health check..."
	docker run --rm --gpus all \
		-v "$$(pwd):/app" \
		trellis:latest \
		conda run -n trellis python docker-healthcheck.py

# Check Docker setup
check-docker:
	@echo "Checking Docker setup..."
	@command -v docker >/dev/null 2>&1 || { echo "Docker is not installed"; exit 1; }
	@docker info >/dev/null 2>&1 || { echo "Docker is not running"; exit 1; }
	@command -v docker-compose >/dev/null 2>&1 || docker compose version >/dev/null 2>&1 || { echo "Docker Compose is not available"; exit 1; }
	@docker run --rm --gpus all nvidia/cuda:11.8-base-ubuntu20.04 nvidia-smi >/dev/null 2>&1 || echo "Warning: NVIDIA Docker runtime may not be properly configured"
	@echo "Docker setup looks good!"

# Development targets
dev-build:
	@echo "Building development image..."
	docker build -t trellis:dev .

dev-run:
	@echo "Running development container..."
	docker run -it --rm --gpus all \
		-v "$$(pwd):/app" \
		-v "$$(pwd)/outputs:/app/outputs" \
		-p 7860:7860 \
		-e CUDA_VISIBLE_DEVICES=0 \
		trellis:dev

# Quick start (build and run)
quick-start: build run

# Full setup (check, build, run)
setup: check-docker build run
