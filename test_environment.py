#!/usr/bin/env python3
"""
Test script for TRELLIS training environment
"""

import sys
import os

def test_basic_imports():
    """Test basic Python imports"""
    print("Testing basic imports...")
    
    try:
        import torch
        print(f"✅ PyTorch version: {torch.__version__}")
        print(f"✅ CUDA available: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"✅ CUDA devices: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                print(f"   Device {i}: {torch.cuda.get_device_name(i)}")
    except ImportError as e:
        print(f"❌ PyTorch import failed: {e}")
        return False
    
    try:
        import numpy as np
        print(f"✅ NumPy version: {np.__version__}")
    except ImportError as e:
        print(f"❌ NumPy import failed: {e}")
        return False
    
    return True

def test_training_dependencies():
    """Test training-specific dependencies"""
    print("\nTesting training dependencies...")
    
    dependencies = [
        'wandb',
        'tensorboard', 
        'matplotlib',
        'seaborn',
        'datasets',
        'huggingface_hub',
        'objaverse'
    ]
    
    for dep in dependencies:
        try:
            __import__(dep)
            print(f"✅ {dep}")
        except ImportError as e:
            print(f"❌ {dep}: {e}")
    
    return True

def test_trellis_import():
    """Test TRELLIS module import"""
    print("\nTesting TRELLIS imports...")

    try:
        import trellis
        print("✅ TRELLIS module imported successfully")

        # Test specific components
        from trellis.pipelines import TrellisImageTo3DPipeline, TrellisTextTo3DPipeline
        print("✅ TrellisImageTo3DPipeline imported")
        print("✅ TrellisTextTo3DPipeline imported")

        # Test model imports
        from trellis.models import SLatEncoder, SLatGaussianDecoder
        print("✅ SLatEncoder imported")
        print("✅ SLatGaussianDecoder imported")

        # Test trainer imports
        from trellis.trainers import BasicTrainer
        print("✅ BasicTrainer imported")

        return True
    except ImportError as e:
        print(f"❌ TRELLIS import failed: {e}")
        return False

def test_model_files():
    """Test if model files are accessible"""
    print("\nTesting model files...")
    
    model_dir = "/app/model"
    if os.path.exists(model_dir):
        print(f"✅ Model directory exists: {model_dir}")
        
        # Check for specific model directories
        models = [
            "JeffreyXiang_TRELLIS-image-large",
            "JeffreyXiang_TRELLIS-text-base", 
            "JeffreyXiang_TRELLIS-text-large",
            "JeffreyXiang_TRELLIS-text-xlarge"
        ]
        
        for model in models:
            model_path = os.path.join(model_dir, model)
            if os.path.exists(model_path):
                print(f"✅ {model}")
                # Check for ckpts directory
                ckpts_path = os.path.join(model_path, "ckpts")
                if os.path.exists(ckpts_path):
                    ckpt_files = os.listdir(ckpts_path)
                    print(f"   Checkpoint files: {len(ckpt_files)}")
                else:
                    print(f"   ❌ No ckpts directory found")
            else:
                print(f"❌ {model} not found")
    else:
        print(f"❌ Model directory not found: {model_dir}")
    
    return True

def test_directories():
    """Test if required directories exist"""
    print("\nTesting directories...")
    
    directories = [
        "/app/outputs",
        "/app/datasets", 
        "/app/tmp",
        "/app/logs"
    ]
    
    for directory in directories:
        if os.path.exists(directory):
            print(f"✅ {directory}")
        else:
            print(f"❌ {directory} not found")
            try:
                os.makedirs(directory, exist_ok=True)
                print(f"   Created {directory}")
            except Exception as e:
                print(f"   Failed to create {directory}: {e}")
    
    return True

def main():
    """Main test function"""
    print("🚀 TRELLIS Training Environment Test")
    print("=" * 50)
    
    tests = [
        test_basic_imports,
        test_training_dependencies,
        test_trellis_import,
        test_model_files,
        test_directories
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    print("📊 Test Summary")
    print("=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"✅ All tests passed ({passed}/{total})")
        print("🎉 Training environment is ready!")
        return 0
    else:
        print(f"❌ Some tests failed ({passed}/{total})")
        print("⚠️  Please check the issues above")
        return 1

if __name__ == "__main__":
    sys.exit(main())
