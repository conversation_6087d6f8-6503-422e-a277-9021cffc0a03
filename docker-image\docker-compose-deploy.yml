services:
  # TRELLIS 推理服务
  trellis-inference:
    image: trellis:latest
    container_name: trellis-inference
    restart: unless-stopped
    ports:
      - "7860:7860"
    volumes:
      - ./outputs:/app/outputs
      - ./model:/app/model
    environment:
      - NVIDIA_VISIBLE_DEVICES=all
      - CUDA_VISIBLE_DEVICES=0
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
    command: >
      bash -c "
        conda run -n trellis python app.py 
        --server-name 0.0.0.0 
        --server-port 7860
      "
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:7860"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # TRELLIS 训练服务
  trellis-training:
    image: trellis-train:latest
    container_name: trellis-training
    restart: unless-stopped
    volumes:
      - ./model:/app/model
      - ./datasets:/app/datasets
      - ./outputs:/app/outputs
      - ./logs:/app/logs
      - ./tmp:/app/tmp
      - ./configs:/app/configs
    environment:
      - NVIDIA_VISIBLE_DEVICES=all
      - CUDA_VISIBLE_DEVICES=0
      - SPCONV_ALGO=native
      - ATTN_BACKEND=xformers
      - PYTHONPATH=/app
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
    command: >
      bash -c "
        echo 'TRELLIS训练环境已启动' &&
        echo '进入容器: docker exec -it trellis-training bash' &&
        echo '激活环境: conda activate trellis' &&
        echo '开始训练: python train.py --config configs/your_config.json' &&
        tail -f /dev/null
      "
    stdin_open: true
    tty: true

  # TensorBoard 服务 (可选)
  tensorboard:
    image: tensorflow/tensorflow:latest-gpu
    container_name: trellis-tensorboard
    restart: unless-stopped
    ports:
      - "6006:6006"
    volumes:
      - ./logs:/logs
      - ./outputs:/outputs
    command: >
      bash -c "
        pip install tensorboard &&
        tensorboard --logdir=/logs --host=0.0.0.0 --port=6006
      "
    profiles:
      - monitoring

networks:
  default:
    name: trellis-network
