#!/usr/bin/env python3
"""
简化的TRELLIS训练测试脚本
验证核心训练组件是否正常工作
"""

import os
import sys
import torch
import numpy as np
import random
from trellis import models, trainers

# 设置环境变量
os.environ['SPCONV_ALGO'] = 'native'
os.environ['ATTN_BACKEND'] = 'xformers'

def setup_rng(seed=42):
    """设置随机种子"""
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    random.seed(seed)

def test_model_forward():
    """测试模型前向传播"""
    print("🔧 测试模型前向传播...")
    
    try:
        # 创建编码器
        encoder = models.SLatEncoder(
            resolution=32,
            in_channels=512,
            model_channels=128,
            latent_channels=4,
            num_blocks=2,
            num_heads=4,
            mlp_ratio=2,
            attn_mode="swin",
            window_size=4,
            use_fp16=True
        ).cuda()
        
        # 创建解码器
        decoder = models.SLatGaussianDecoder(
            resolution=32,
            model_channels=128,
            latent_channels=4,
            num_blocks=2,
            num_heads=4,
            mlp_ratio=2,
            attn_mode="swin",
            window_size=4,
            use_fp16=True,
            representation_config={
                "lr": {
                    "_xyz": 1.0,
                    "_features_dc": 1.0,
                    "_opacity": 1.0,
                    "_scaling": 1.0,
                    "_rotation": 0.1
                },
                "perturb_offset": True,
                "voxel_size": 1.5,
                "num_gaussians": 8,
                "2d_filter_kernel_size": 0.1,
                "3d_filter_kernel_size": 9e-4,
                "scaling_bias": 4e-3,
                "opacity_bias": 0.1,
                "scaling_activation": "softplus"
            }
        ).cuda()
        
        print(f"  编码器参数: {sum(p.numel() for p in encoder.parameters()):,}")
        print(f"  解码器参数: {sum(p.numel() for p in decoder.parameters()):,}")
        
        # 创建测试数据
        batch_size = 1
        num_voxels = 100
        
        # 稀疏坐标 (batch_idx, x, y, z)
        coords = torch.randint(0, 32, (num_voxels, 3), dtype=torch.int32)
        batch_coords = torch.cat([
            torch.zeros(num_voxels, 1, dtype=torch.int32),
            coords
        ], dim=1).cuda()
        
        # 稀疏特征
        features = torch.randn(num_voxels, 512, dtype=torch.float32).cuda()
        
        print("  测试编码器前向传播...")
        with torch.no_grad():
            latent = encoder(features, batch_coords)
            print(f"    潜在表示形状: {latent.shape}")
        
        print("  测试解码器前向传播...")
        with torch.no_grad():
            # 创建相机参数
            cameras = torch.randn(batch_size, 6, 16, dtype=torch.float32).cuda()
            
            # 解码
            output = decoder(latent, cameras)
            print(f"    输出类型: {type(output)}")
            if isinstance(output, dict):
                for key, value in output.items():
                    if isinstance(value, torch.Tensor):
                        print(f"      {key}: {value.shape}")
        
        print("✅ 模型前向传播测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 模型前向传播测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_loss_computation():
    """测试损失计算"""
    print("\n🎯 测试损失计算...")
    
    try:
        # 创建简单的损失函数
        l1_loss = torch.nn.L1Loss()
        
        # 创建测试数据
        pred = torch.randn(4, 3, 64, 64).cuda()
        target = torch.randn(4, 3, 64, 64).cuda()
        
        # 计算损失
        loss = l1_loss(pred, target)
        print(f"  L1损失: {loss.item():.6f}")
        
        # 测试梯度
        loss.backward()
        print("  梯度计算成功")
        
        print("✅ 损失计算测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 损失计算测试失败: {e}")
        return False

def test_optimizer():
    """测试优化器"""
    print("\n⚙️ 测试优化器...")
    
    try:
        # 创建简单模型
        model = torch.nn.Linear(10, 1).cuda()
        
        # 创建优化器
        optimizer = torch.optim.AdamW(model.parameters(), lr=1e-4)
        
        # 创建测试数据
        x = torch.randn(32, 10).cuda()
        y = torch.randn(32, 1).cuda()
        
        # 前向传播
        pred = model(x)
        loss = torch.nn.MSELoss()(pred, y)
        
        # 反向传播
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        
        print(f"  初始损失: {loss.item():.6f}")
        
        # 再次前向传播检查损失变化
        pred = model(x)
        new_loss = torch.nn.MSELoss()(pred, y)
        print(f"  优化后损失: {new_loss.item():.6f}")
        
        print("✅ 优化器测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 优化器测试失败: {e}")
        return False

def test_cuda_memory():
    """测试CUDA内存管理"""
    print("\n💾 测试CUDA内存管理...")
    
    try:
        # 检查初始内存
        initial_memory = torch.cuda.memory_allocated() / 1024**2
        print(f"  初始内存使用: {initial_memory:.1f} MB")
        
        # 分配大量内存
        large_tensor = torch.randn(1000, 1000, device='cuda')
        allocated_memory = torch.cuda.memory_allocated() / 1024**2
        print(f"  分配后内存使用: {allocated_memory:.1f} MB")
        
        # 释放内存
        del large_tensor
        torch.cuda.empty_cache()
        final_memory = torch.cuda.memory_allocated() / 1024**2
        print(f"  释放后内存使用: {final_memory:.1f} MB")
        
        print("✅ CUDA内存管理测试成功")
        return True
        
    except Exception as e:
        print(f"❌ CUDA内存管理测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 TRELLIS 简化训练测试")
    print("=" * 60)
    
    # 设置随机种子
    setup_rng(42)
    
    # 检查CUDA
    if not torch.cuda.is_available():
        print("❌ CUDA不可用")
        return False
    
    print(f"🔥 CUDA设备: {torch.cuda.get_device_name()}")
    print(f"🔥 CUDA内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
    
    # 运行测试
    tests = [
        test_model_forward,
        test_loss_computation,
        test_optimizer,
        test_cuda_memory
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append(False)
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试总结")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"✅ 所有测试通过 ({passed}/{total})")
        print("🎉 TRELLIS核心训练组件正常工作！")
        print("\n📝 可以开始真实训练:")
        print("1. 准备训练数据集")
        print("2. 选择配置文件")
        print("3. 运行训练命令:")
        print("   python train.py --config configs/vae/slat_vae_enc_dec_gs_swin8_B_64l8_fp16.json")
        return True
    else:
        print(f"❌ 部分测试失败 ({passed}/{total})")
        print("请检查失败的测试并修复问题")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
