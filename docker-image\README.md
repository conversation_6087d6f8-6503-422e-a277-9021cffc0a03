# TRELLIS Docker 一键部署包

这是TRELLIS 3D生成模型的完整Docker部署包，包含推理和训练环境。

## 📦 包含内容

### Docker镜像
- `trellis-inference.tar` - 推理镜像 (约15GB)
- `trellis-training.tar` - 训练镜像 (约16GB)

### 部署脚本
- `deploy-windows.bat` - Windows一键部署脚本
- `deploy-linux.sh` - Linux/Ubuntu一键部署脚本
- `docker-compose-deploy.yml` - Docker Compose配置文件

### 预训练模型和配置
- `model/` - 预训练模型文件夹 (包含4个TRELLIS模型)
- `configs/` - 训练配置文件夹 (VAE和生成模型配置)

### 工作目录
- `outputs/` - 生成输出目录
- `datasets/` - 训练数据集目录
- `logs/` - 训练日志目录
- `tmp/` - 临时文件目录

### 文档和测试
- `README.md` - 详细使用说明
- `test-deployment.bat` / `test-deployment.sh` - 部署测试脚本

## 🚀 快速开始

### Windows用户

1. 确保已安装 [Docker Desktop](https://www.docker.com/products/docker-desktop)
2. 双击运行 `deploy-windows.bat`
3. 按提示选择部署模式

### Linux/Ubuntu用户

1. 确保已安装Docker和Docker Compose
2. 给脚本执行权限：`chmod +x deploy-linux.sh`
3. 运行脚本：`./deploy-linux.sh`
4. 按提示选择部署模式

## 📋 部署模式

### 1. 推理模式
- 启动Web界面，端口7860
- 支持图像到3D和文本到3D生成
- 访问地址：http://localhost:7860

### 2. 训练模式
- 启动训练环境
- 支持VAE和生成模型训练
- 进入容器：`docker exec -it trellis-training bash`

### 3. 完整模式
- 同时启动推理和训练环境
- 推理界面：http://localhost:7860
- 训练环境：`docker exec -it trellis-training bash`

### 4. 加载镜像
- 仅加载Docker镜像到本地
- 不启动任何服务

### 5. 清理环境
- 停止并删除所有容器
- 清理Docker缓存

## 💻 系统要求

### 最低要求
- **GPU**: NVIDIA GPU (支持CUDA 11.8+)
- **显存**: 8GB+ (推理) / 16GB+ (训练)
- **内存**: 16GB+ (推理) / 32GB+ (训练)
- **存储**: 100GB+ 可用空间

### 推荐配置
- **GPU**: RTX 3080/4080/4090 或更高
- **显存**: 24GB+
- **内存**: 64GB+
- **存储**: SSD 200GB+

## 🔧 手动部署

如果自动脚本无法使用，可以手动执行以下命令：

### 加载镜像
```bash
# 加载推理镜像
docker load -i trellis-inference.tar

# 加载训练镜像
docker load -i trellis-training.tar
```

### 启动服务
```bash
# 推理模式
docker compose -f docker-compose-deploy.yml up -d trellis-inference

# 训练模式
docker compose -f docker-compose-deploy.yml up -d trellis-training

# 完整模式
docker compose -f docker-compose-deploy.yml up -d
```

## 📁 目录结构

部署后会创建以下目录：

```
docker-image/
├── model/          # 预训练模型
├── datasets/       # 训练数据集
├── outputs/        # 生成输出
├── logs/          # 训练日志
├── tmp/           # 临时文件
└── configs/       # 训练配置
```

## 🎯 使用说明

### 推理使用
1. 访问 http://localhost:7860
2. 上传图像或输入文本描述
3. 调整生成参数
4. 点击生成按钮
5. 下载生成的3D模型

### 训练使用
1. 进入训练容器：`docker exec -it trellis-training bash`
2. 激活环境：`conda activate trellis`
3. 准备数据集到 `datasets/` 目录
4. 选择配置文件：`configs/vae/` 或 `configs/generation/`
5. 开始训练：
   ```bash
   # VAE训练
   python train.py --config configs/vae/slat_vae_enc_dec_gs_swin8_B_64l8_fp16.json
   
   # 生成模型训练
   python train.py --config configs/generation/slat_flow_img_dit_L_64l8p2_fp16.json
   ```

## 📊 监控和日志

### 查看容器状态
```bash
docker ps
```

### 查看日志
```bash
# 推理服务日志
docker logs -f trellis-inference

# 训练服务日志
docker logs -f trellis-training
```

### TensorBoard监控
```bash
# 启动TensorBoard (可选)
docker compose -f docker-compose-deploy.yml --profile monitoring up -d tensorboard

# 访问 http://localhost:6006
```

## 🔍 故障排除

### 常见问题

1. **端口被占用**
   - 修改 `docker-compose-deploy.yml` 中的端口映射
   - 或停止占用端口的程序

2. **GPU不可用**
   - 确保安装了NVIDIA驱动
   - 安装nvidia-docker2：`sudo apt install nvidia-docker2`
   - 重启Docker服务：`sudo systemctl restart docker`

3. **内存不足**
   - 关闭其他程序释放内存
   - 减小批处理大小
   - 使用梯度检查点

4. **镜像加载失败**
   - 检查磁盘空间是否充足
   - 确保tar文件完整未损坏
   - 重新下载镜像文件

### 重置环境
```bash
# 停止所有容器
docker compose -f docker-compose-deploy.yml down

# 清理系统
docker system prune -a -f

# 重新加载镜像
docker load -i trellis-inference.tar
docker load -i trellis-training.tar
```

## 📞 技术支持

如遇问题，请检查：
1. Docker和GPU驱动是否正确安装
2. 系统资源是否满足要求
3. 网络连接是否正常
4. 镜像文件是否完整

## 📄 许可证

本部署包基于TRELLIS项目，遵循相应的开源许可证。

---

**🎉 享受TRELLIS 3D生成的强大功能！**
