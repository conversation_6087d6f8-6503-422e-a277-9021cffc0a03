# TRELLIS Docker Setup Guide

This guide will help you set up and run TRELLIS using Docker, which provides a consistent environment across different systems.

## Prerequisites

### System Requirements
- **Docker**: Version 20.10 or higher
- **Docker Compose**: Version 2.0 or higher  
- **NVIDIA Docker Runtime**: For GPU support
- **NVIDIA GPU**: With at least 16GB VRAM (tested on A100, A6000)
- **System Memory**: At least 32GB RAM recommended

### Install Docker and NVIDIA Container Toolkit

#### Ubuntu/Debian
```bash
# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install NVIDIA Container Toolkit
distribution=$(. /etc/os-release;echo $ID$VERSION_ID)
curl -s -L https://nvidia.github.io/nvidia-docker/gpgkey | sudo apt-key add -
curl -s -L https://nvidia.github.io/nvidia-docker/$distribution/nvidia-docker.list | sudo tee /etc/apt/sources.list.d/nvidia-docker.list

sudo apt-get update && sudo apt-get install -y nvidia-container-toolkit
sudo systemctl restart docker
```

#### Other Systems
Please refer to:
- [Docker Installation Guide](https://docs.docker.com/get-docker/)
- [NVIDIA Container Toolkit Installation](https://docs.nvidia.com/datacenter/cloud-native/container-toolkit/install-guide.html)

## Quick Start

### 1. Clone the Repository
```bash
git clone --recurse-submodules https://github.com/microsoft/TRELLIS.git
cd TRELLIS
```

### 2. Build the Docker Image
```bash
# Using the provided script (recommended)
./docker-run.sh build

# Or using docker-compose directly
docker-compose build
```

### 3. Run TRELLIS Demo
```bash
# Run the main image-to-3D demo
./docker-run.sh run

# Or run text-to-3D demo
./docker-run.sh run-text

# Or run a simple example
./docker-run.sh example
```

### 4. Access the Web Interface
Open your browser and navigate to:
- Main demo: http://localhost:7860
- Text demo: http://localhost:7861

## Usage Commands

The `docker-run.sh` script provides several convenient commands:

### Build Commands
```bash
# Build the Docker image
./docker-run.sh build
```

### Run Commands
```bash
# Run image-to-3D demo (default port 7860)
./docker-run.sh run

# Run text-to-3D demo (port 7861)
./docker-run.sh run-text

# Run example script
./docker-run.sh example

# Open interactive shell
./docker-run.sh shell
```

### Management Commands
```bash
# Stop all containers
./docker-run.sh stop

# View logs
./docker-run.sh logs

# Clean up containers and images
./docker-run.sh clean
```

### Options
```bash
# Specify GPU ID (default: 0)
./docker-run.sh run --gpu 1

# Specify custom port (default: 7860)
./docker-run.sh run --port 8080

# Show help
./docker-run.sh --help
```

## Manual Docker Commands

If you prefer to use Docker commands directly:

### Build Image
```bash
docker-compose build
```

### Run Main Demo
```bash
docker-compose up trellis
```

### Run Text Demo
```bash
docker-compose --profile text up trellis-text
```

### Run Example
```bash
docker-compose --profile example up trellis-example
```

### Interactive Shell
```bash
docker run -it --rm --gpus all \
  -v "$(pwd):/app" \
  -v "$(pwd)/outputs:/app/outputs" \
  -e CUDA_VISIBLE_DEVICES=0 \
  trellis:latest \
  conda run -n trellis bash
```

## Directory Structure

The Docker setup creates the following directory structure:

```
TRELLIS/
├── Dockerfile              # Docker image definition
├── docker-compose.yml      # Docker Compose configuration
├── docker-run.sh          # Convenience script
├── .dockerignore          # Files to ignore during build
├── outputs/               # Generated 3D models (mounted)
├── tmp/                   # Temporary files (mounted)
└── ...                    # Project files
```

## Environment Variables

The following environment variables are set in the container:

- `ATTN_BACKEND=flash-attn`: Use flash attention backend
- `SPCONV_ALGO=native`: Use native spconv algorithm
- `CUDA_VISIBLE_DEVICES=0`: GPU device to use

## Volumes and Data Persistence

The Docker setup mounts several directories:

- `./outputs:/app/outputs`: Generated 3D models
- `./tmp:/app/tmp`: Temporary files
- `trellis-cache:/root/.cache`: Model cache (persistent volume)

## Troubleshooting

### Common Issues

1. **NVIDIA Docker not working**
   ```bash
   # Test NVIDIA Docker
   docker run --rm --gpus all nvidia/cuda:11.8-base-ubuntu20.04 nvidia-smi
   ```

2. **Out of memory errors**
   - Ensure you have at least 16GB GPU memory
   - Close other GPU-intensive applications
   - Try using a smaller model if available

3. **Port already in use**
   ```bash
   # Use a different port
   ./docker-run.sh run --port 8080
   ```

4. **Permission denied**
   ```bash
   # Make sure docker-run.sh is executable
   chmod +x docker-run.sh
   
   # Add user to docker group
   sudo usermod -aG docker $USER
   # Then logout and login again
   ```

### Getting Help

- Check container logs: `./docker-run.sh logs`
- Open interactive shell: `./docker-run.sh shell`
- Check GPU status in container: `nvidia-smi`

## Performance Notes

- First run will download models (~several GB)
- Model loading takes 1-2 minutes on first startup
- Generation time depends on GPU and complexity
- Use SSD storage for better I/O performance

## Development

For development purposes, the current directory is mounted into the container, so code changes are reflected immediately without rebuilding the image.

```bash
# Open development shell
./docker-run.sh shell

# Make changes to code, then restart service
./docker-run.sh stop
./docker-run.sh run
```
