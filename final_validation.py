#!/usr/bin/env python3
"""
TRELLIS 最终验证脚本
验证训练环境的核心功能
"""

import os
import sys
import torch
import numpy as np
import random

# 设置环境变量
os.environ['SPCONV_ALGO'] = 'native'
os.environ['ATTN_BACKEND'] = 'xformers'

def setup_rng(seed=42):
    """设置随机种子"""
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    random.seed(seed)

def test_basic_imports():
    """测试基本导入"""
    print("📦 测试基本导入...")
    
    try:
        import torch
        print(f"  ✅ PyTorch {torch.__version__}")
        
        import trellis
        print("  ✅ TRELLIS")
        
        from trellis import models, trainers, datasets, pipelines
        print("  ✅ TRELLIS子模块")
        
        # 测试具体类的导入
        from trellis.models import SLatEncoder, SLatGaussianDecoder, SLatFlowModel
        print("  ✅ 模型类")
        
        from trellis.trainers import BasicTrainer, SLatVaeGaussianTrainer
        print("  ✅ 训练器类")
        
        from trellis.pipelines import TrellisImageTo3DPipeline, TrellisTextTo3DPipeline
        print("  ✅ Pipeline类")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 导入失败: {e}")
        return False

def test_cuda_environment():
    """测试CUDA环境"""
    print("\n🔥 测试CUDA环境...")
    
    try:
        if not torch.cuda.is_available():
            print("  ❌ CUDA不可用")
            return False
        
        device_count = torch.cuda.device_count()
        print(f"  ✅ CUDA设备数量: {device_count}")
        
        for i in range(device_count):
            device_name = torch.cuda.get_device_name(i)
            device_props = torch.cuda.get_device_properties(i)
            memory_gb = device_props.total_memory / 1024**3
            print(f"    设备 {i}: {device_name} ({memory_gb:.1f} GB)")
        
        # 测试基本CUDA操作
        x = torch.randn(1000, 1000, device='cuda')
        y = torch.randn(1000, 1000, device='cuda')
        z = torch.matmul(x, y)
        
        print(f"  ✅ CUDA矩阵运算成功")
        print(f"  ✅ 内存使用: {torch.cuda.memory_allocated() / 1024**2:.1f} MB")
        
        # 清理
        del x, y, z
        torch.cuda.empty_cache()
        
        return True
        
    except Exception as e:
        print(f"  ❌ CUDA测试失败: {e}")
        return False

def test_model_creation():
    """测试模型创建"""
    print("\n🔧 测试模型创建...")
    
    try:
        from trellis.models import SLatEncoder, SLatGaussianDecoder, SLatFlowModel
        
        # 创建编码器（使用最小配置）
        encoder = SLatEncoder(
            resolution=16,  # 最小分辨率
            in_channels=256,  # 减小输入通道
            model_channels=64,  # 最小模型通道
            latent_channels=4,
            num_blocks=1,  # 最少块数
            num_heads=2,
            mlp_ratio=2,
            attn_mode="swin",
            window_size=4,
            use_fp16=False  # 禁用FP16避免问题
        )
        
        encoder_params = sum(p.numel() for p in encoder.parameters())
        print(f"  ✅ 编码器创建成功 ({encoder_params:,} 参数)")
        
        # 创建解码器
        decoder = SLatGaussianDecoder(
            resolution=16,
            model_channels=64,
            latent_channels=4,
            num_blocks=1,
            num_heads=2,
            mlp_ratio=2,
            attn_mode="swin",
            window_size=4,
            use_fp16=False,
            representation_config={
                "lr": {
                    "_xyz": 1.0,
                    "_features_dc": 1.0,
                    "_opacity": 1.0,
                    "_scaling": 1.0,
                    "_rotation": 0.1
                },
                "perturb_offset": True,
                "voxel_size": 1.5,
                "num_gaussians": 4,  # 最少高斯数
                "2d_filter_kernel_size": 0.1,
                "3d_filter_kernel_size": 9e-4,
                "scaling_bias": 4e-3,
                "opacity_bias": 0.1,
                "scaling_activation": "softplus"
            }
        )
        
        decoder_params = sum(p.numel() for p in decoder.parameters())
        print(f"  ✅ 解码器创建成功 ({decoder_params:,} 参数)")
        
        # 创建流模型
        flow_model = SLatFlowModel(
            resolution=16,
            in_channels=4,
            out_channels=4,
            model_channels=64,
            cond_channels=256,
            num_blocks=1,
            num_heads=2,
            mlp_ratio=2,
            patch_size=2,
            num_io_res_blocks=1,
            io_block_channels=[32],
            pe_mode="ape",
            qk_rms_norm=True,
            use_fp16=False
        )
        
        flow_params = sum(p.numel() for p in flow_model.parameters())
        print(f"  ✅ 流模型创建成功 ({flow_params:,} 参数)")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 模型创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_training_dependencies():
    """测试训练依赖"""
    print("\n📚 测试训练依赖...")
    
    dependencies = [
        ('wandb', 'W&B日志'),
        ('tensorboard', 'TensorBoard'),
        ('matplotlib', 'Matplotlib'),
        ('seaborn', 'Seaborn'),
        ('datasets', 'HuggingFace Datasets'),
        ('huggingface_hub', 'HuggingFace Hub'),
        ('lpips', 'LPIPS损失'),
        ('cv2', 'OpenCV'),
        ('skimage', 'Scikit-Image')
    ]
    
    success_count = 0
    for module, name in dependencies:
        try:
            __import__(module)
            print(f"  ✅ {name}")
            success_count += 1
        except ImportError:
            print(f"  ❌ {name}")
    
    print(f"  📊 依赖检查: {success_count}/{len(dependencies)} 成功")
    return success_count == len(dependencies)

def test_configuration_loading():
    """测试配置文件加载"""
    print("\n⚙️ 测试配置文件...")
    
    try:
        import json
        from pathlib import Path
        
        config_dir = Path("configs")
        if not config_dir.exists():
            print("  ⚠️  配置目录不存在")
            return True  # 不是致命错误
        
        # 查找配置文件
        config_files = list(config_dir.rglob("*.json"))
        print(f"  📁 找到 {len(config_files)} 个配置文件")
        
        # 测试加载几个配置文件
        for config_file in config_files[:3]:  # 只测试前3个
            try:
                with open(config_file, 'r') as f:
                    config = json.load(f)
                print(f"  ✅ {config_file.name}")
            except Exception as e:
                print(f"  ❌ {config_file.name}: {e}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 配置测试失败: {e}")
        return False

def test_model_files():
    """测试模型文件"""
    print("\n📂 测试模型文件...")
    
    try:
        from pathlib import Path
        
        model_dir = Path("model")
        if not model_dir.exists():
            print("  ⚠️  模型目录不存在")
            return True
        
        # 检查预训练模型
        model_names = [
            "JeffreyXiang_TRELLIS-image-large",
            "JeffreyXiang_TRELLIS-text-base",
            "JeffreyXiang_TRELLIS-text-large",
            "JeffreyXiang_TRELLIS-text-xlarge"
        ]
        
        for model_name in model_names:
            model_path = model_dir / model_name
            if model_path.exists():
                ckpts_path = model_path / "ckpts"
                if ckpts_path.exists():
                    ckpt_files = list(ckpts_path.glob("*.pt"))
                    print(f"  ✅ {model_name} ({len(ckpt_files)} 检查点)")
                else:
                    print(f"  ⚠️  {model_name} (无检查点目录)")
            else:
                print(f"  ❌ {model_name} (不存在)")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 模型文件测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 TRELLIS 最终验证")
    print("=" * 60)
    
    # 设置随机种子
    setup_rng(42)
    
    # 运行所有测试
    tests = [
        ("基本导入", test_basic_imports),
        ("CUDA环境", test_cuda_environment),
        ("模型创建", test_model_creation),
        ("训练依赖", test_training_dependencies),
        ("配置文件", test_configuration_loading),
        ("模型文件", test_model_files)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 验证总结")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅" if result else "❌"
        print(f"{status} {test_name}")
    
    print(f"\n📈 总体结果: {passed}/{total} 测试通过")
    
    if passed >= total - 1:  # 允许1个非关键测试失败
        print("🎉 TRELLIS训练环境验证成功！")
        print("\n📝 环境已准备就绪，可以开始训练:")
        print("1. 准备训练数据集")
        print("2. 选择合适的配置文件")
        print("3. 运行训练命令:")
        print("   python train.py --config configs/vae/slat_vae_enc_dec_gs_swin8_B_64l8_fp16.json")
        print("   python train.py --config configs/generation/slat_flow_img_dit_L_64l8p2_fp16.json")
        return True
    else:
        print("❌ 验证失败，请检查失败的测试")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
