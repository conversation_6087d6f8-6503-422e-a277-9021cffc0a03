{"models": {"denoiser": {"name": "SLatFlowModel", "args": {"resolution": 64, "in_channels": 8, "out_channels": 8, "model_channels": 256, "cond_channels": 1024, "num_blocks": 6, "num_heads": 8, "mlp_ratio": 4, "patch_size": 2, "num_io_res_blocks": 2, "io_block_channels": [128], "pe_mode": "ape", "qk_rms_norm": true, "use_fp16": true}}}, "dataset": {"name": "ImageConditionedSLat", "args": {"latent_model": "dinov2_vitl14_reg_slat_enc_swin8_B_64l8_fp16", "min_aesthetic_score": 4.5, "max_num_voxels": 8192, "image_size": 256, "normalization": {"mean": [-2.17, -0.004, -0.13, -0.08, -0.53, 0.72, -1.14, 1.2], "std": [2.38, 2.39, 2.12, 2.17, 2.66, 2.37, 2.62, 2.68]}}}, "trainer": {"name": "ImageConditionedSparseFlowMatchingCFGTrainer", "args": {"max_steps": 100, "batch_size_per_gpu": 1, "batch_split": 1, "optimizer": {"name": "AdamW", "args": {"lr": 0.0001, "weight_decay": 0.0}}, "ema_rate": [0.9999], "fp16_mode": "inflat_all", "fp16_scale_growth": 0.001, "grad_clip": 1.0, "i_log": 10, "i_sample": 50, "i_save": 50, "p_uncond": 0.1, "t_schedule": {"name": "logitNormal", "args": {"mean": 1.0, "std": 1.0}}, "sigma_min": 1e-05, "image_cond_model": "dinov2_vitl14_reg"}}}