@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    TRELLIS Docker 一键部署脚本 (Windows)
echo ========================================
echo.

:: 检查Docker是否安装
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker未安装或未启动，请先安装Docker Desktop
    echo 下载地址: https://www.docker.com/products/docker-desktop
    pause
    exit /b 1
)

echo ✅ Docker已安装并运行

:: 检查Docker Compose是否可用
docker compose version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker Compose不可用
    pause
    exit /b 1
)

echo ✅ Docker Compose可用

:: 检查镜像文件是否存在
if not exist "trellis-inference.tar" (
    echo ❌ 推理镜像文件 trellis-inference.tar 不存在
    pause
    exit /b 1
)

if not exist "trellis-training.tar" (
    echo ❌ 训练镜像文件 trellis-training.tar 不存在
    pause
    exit /b 1
)

:: 检查必要的文件夹
if not exist "model" (
    echo ❌ 模型文件夹 model 不存在
    pause
    exit /b 1
)

if not exist "configs" (
    echo ❌ 配置文件夹 configs 不存在
    pause
    exit /b 1
)

:: 创建必要的目录
if not exist "outputs" mkdir outputs
if not exist "datasets" mkdir datasets
if not exist "logs" mkdir logs
if not exist "tmp" mkdir tmp

echo ✅ 文件和目录检查完成

echo.
echo 请选择部署模式:
echo 1. 推理模式 (启动Web界面，端口7860)
echo 2. 训练模式 (启动训练环境)
echo 3. 完整模式 (同时启动推理和训练)
echo 4. 加载镜像 (仅加载Docker镜像)
echo 5. 清理环境 (停止并删除容器)
echo.
set /p choice="请输入选择 (1-5): "

if "%choice%"=="1" goto inference
if "%choice%"=="2" goto training
if "%choice%"=="3" goto full
if "%choice%"=="4" goto load_images
if "%choice%"=="5" goto cleanup
echo 无效选择，退出
pause
exit /b 1

:load_images
echo.
echo 🔄 加载Docker镜像...
echo 正在加载推理镜像 (约45GB)...
docker load -i trellis-inference.tar
if %errorlevel% neq 0 (
    echo ❌ 推理镜像加载失败
    pause
    exit /b 1
)

echo 正在加载训练镜像 (约45.7GB)...
docker load -i trellis-training.tar
if %errorlevel% neq 0 (
    echo ❌ 训练镜像加载失败
    pause
    exit /b 1
)

echo ✅ 所有镜像加载完成
echo.
echo 可用镜像:
docker images | findstr trellis
echo.
echo 镜像加载完成！现在可以选择其他部署模式。
pause
goto :eof

:inference
echo.
echo 🚀 启动推理模式...
call :load_images_if_needed
docker compose -f docker-compose-deploy.yml up -d trellis-inference
if %errorlevel% neq 0 (
    echo ❌ 推理服务启动失败
    pause
    exit /b 1
)

echo.
echo ✅ TRELLIS推理服务已启动！
echo 🌐 Web界面: http://localhost:7860
echo 📊 状态检查: docker ps
echo 📋 查看日志: docker logs trellis-inference
echo.
echo 正在等待服务启动...
timeout /t 10 /nobreak >nul
start http://localhost:7860
pause
goto :eof

:training
echo.
echo 🏋️ 启动训练模式...
call :load_images_if_needed
docker compose -f docker-compose-deploy.yml up -d trellis-training
if %errorlevel% neq 0 (
    echo ❌ 训练服务启动失败
    pause
    exit /b 1
)

echo.
echo ✅ TRELLIS训练环境已启动！
echo 🔧 进入容器: docker exec -it trellis-training bash
echo 📊 状态检查: docker ps
echo 📋 查看日志: docker logs trellis-training
echo.
echo 训练命令示例:
echo   conda activate trellis
echo   python train.py --config configs/vae/slat_vae_enc_dec_gs_swin8_B_64l8_fp16.json
pause
goto :eof

:full
echo.
echo 🚀 启动完整模式...
call :load_images_if_needed
docker compose -f docker-compose-deploy.yml up -d
if %errorlevel% neq 0 (
    echo ❌ 服务启动失败
    pause
    exit /b 1
)

echo.
echo ✅ TRELLIS完整环境已启动！
echo 🌐 推理界面: http://localhost:7860
echo 🏋️ 训练环境: docker exec -it trellis-training bash
echo 📊 状态检查: docker ps
echo.
timeout /t 10 /nobreak >nul
start http://localhost:7860
pause
goto :eof

:cleanup
echo.
echo 🧹 清理环境...
docker compose -f docker-compose-deploy.yml down
docker system prune -f
echo ✅ 环境清理完成
pause
goto :eof

:load_images_if_needed
:: 检查镜像是否已加载
docker images | findstr "trellis.*latest" >nul 2>&1
if %errorlevel% neq 0 (
    echo 🔄 首次运行，正在加载镜像...
    call :load_images
)
goto :eof
