{"models": {"encoder": {"name": "SLatEncoder", "args": {"resolution": 64, "in_channels": 1024, "model_channels": 256, "latent_channels": 8, "num_blocks": 4, "num_heads": 8, "mlp_ratio": 4, "attn_mode": "swin", "window_size": 8, "use_fp16": true}}, "decoder": {"name": "SLatGaussianDecoder", "args": {"resolution": 64, "model_channels": 256, "latent_channels": 8, "num_blocks": 4, "num_heads": 8, "mlp_ratio": 4, "attn_mode": "swin", "window_size": 8, "use_fp16": true, "representation_config": {"lr": {"_xyz": 1.0, "_features_dc": 1.0, "_opacity": 1.0, "_scaling": 1.0, "_rotation": 0.1}, "perturb_offset": true, "voxel_size": 1.5, "num_gaussians": 16, "2d_filter_kernel_size": 0.1, "3d_filter_kernel_size": 0.0009, "scaling_bias": 0.004, "opacity_bias": 0.1, "scaling_activation": "softplus"}}}}, "dataset": {"name": "SparseFeat2Render", "args": {"image_size": 256, "model": "dinov2_vitl14_reg", "resolution": 64, "min_aesthetic_score": 4.5, "max_num_voxels": 8192}}, "trainer": {"name": "SLatVaeGaussianTrainer", "args": {"max_steps": 100, "batch_size_per_gpu": 1, "batch_split": 1, "optimizer": {"name": "AdamW", "args": {"lr": 0.0001, "weight_decay": 0.0}}, "ema_rate": [0.9999], "fp16_mode": "inflat_all", "fp16_scale_growth": 0.001, "grad_clip": 1.0, "i_log": 10, "i_sample": 50, "i_save": 50, "loss_type": "l1", "lambda_ssim": 0.2, "lambda_lpips": 0.2, "lambda_kl": 1e-06, "regularizations": {"lambda_vol": 1000.0, "lambda_opacity": 0.001}}}}