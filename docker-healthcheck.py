#!/usr/bin/env python3
"""
Health check script for TRELLIS Docker container
"""

import sys
import subprocess
import importlib.util

def check_python_packages():
    """Check if required Python packages are installed"""
    required_packages = [
        'torch',
        'torchvision', 
        'gradio',
        'PIL',
        'numpy',
        'imageio',
        'trimesh',
        'open3d',
        'xformers',
        'spconv'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            if package == 'PIL':
                import PIL
            else:
                __import__(package)
            print(f"✓ {package} is available")
        except ImportError:
            missing_packages.append(package)
            print(f"✗ {package} is missing")
    
    return len(missing_packages) == 0

def check_cuda():
    """Check CUDA availability"""
    try:
        import torch
        if torch.cuda.is_available():
            device_count = torch.cuda.device_count()
            current_device = torch.cuda.current_device()
            device_name = torch.cuda.get_device_name(current_device)
            print(f"✓ CUDA is available")
            print(f"  - Device count: {device_count}")
            print(f"  - Current device: {current_device}")
            print(f"  - Device name: {device_name}")
            return True
        else:
            print("✗ CUDA is not available")
            return False
    except Exception as e:
        print(f"✗ Error checking CUDA: {e}")
        return False

def check_trellis_import():
    """Check if TRELLIS modules can be imported"""
    try:
        from trellis.pipelines import TrellisImageTo3DPipeline
        print("✓ TRELLIS pipelines can be imported")
        return True
    except ImportError as e:
        print(f"✗ Cannot import TRELLIS pipelines: {e}")
        return False

def check_extensions():
    """Check if compiled extensions are working"""
    extensions_status = []
    
    # Check flash-attn
    try:
        import flash_attn
        print("✓ flash-attn is available")
        extensions_status.append(True)
    except ImportError:
        print("✗ flash-attn is not available")
        extensions_status.append(False)
    
    # Check xformers
    try:
        import xformers
        print("✓ xformers is available")
        extensions_status.append(True)
    except ImportError:
        print("✗ xformers is not available")
        extensions_status.append(False)
    
    # Check spconv
    try:
        import spconv
        print("✓ spconv is available")
        extensions_status.append(True)
    except ImportError:
        print("✗ spconv is not available")
        extensions_status.append(False)
    
    return all(extensions_status)

def main():
    """Main health check function"""
    print("TRELLIS Docker Health Check")
    print("=" * 40)
    
    checks = [
        ("Python packages", check_python_packages),
        ("CUDA support", check_cuda),
        ("TRELLIS import", check_trellis_import),
        ("Extensions", check_extensions),
    ]
    
    all_passed = True
    for check_name, check_func in checks:
        print(f"\nChecking {check_name}...")
        try:
            result = check_func()
            if not result:
                all_passed = False
        except Exception as e:
            print(f"✗ Error during {check_name} check: {e}")
            all_passed = False
    
    print("\n" + "=" * 40)
    if all_passed:
        print("✓ All health checks passed!")
        sys.exit(0)
    else:
        print("✗ Some health checks failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
