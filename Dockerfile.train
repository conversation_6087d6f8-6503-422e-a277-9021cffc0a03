# TRELLIS Training Docker Environment
# Base on existing TRELLIS image
FROM trellis:latest

# Switch to conda environment
SHELL ["conda", "run", "-n", "trellis", "/bin/bash", "-c"]

# Install additional training dependencies
RUN pip install wandb tensorboard matplotlib seaborn datasets huggingface_hub objaverse lpips scikit-image opencv-python

# Install additional system tools for training
RUN apt-get update && apt-get install -y htop tmux vim rsync && \
    rm -rf /var/lib/apt/lists/* && apt-get clean

# Set working directory
WORKDIR /app

# Set environment variables for TRELLIS training
ENV ATTN_BACKEND=xformers
ENV SPCONV_ALGO=native
ENV PYTHONPATH=/app

# Create directories for training outputs, datasets, and temporary files
RUN mkdir -p /app/outputs /app/datasets /app/tmp /app/logs

# Default command for training
CMD ["conda", "run", "-n", "trellis", "bash"]
