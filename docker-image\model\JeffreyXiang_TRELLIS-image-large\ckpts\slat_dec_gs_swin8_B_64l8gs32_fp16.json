{"name": "SLatGaussianDecoder", "args": {"resolution": 64, "model_channels": 768, "latent_channels": 8, "num_blocks": 12, "num_heads": 12, "mlp_ratio": 4, "attn_mode": "swin", "window_size": 8, "use_fp16": true, "representation_config": {"lr": {"_xyz": 1.0, "_features_dc": 1.0, "_opacity": 1.0, "_scaling": 1.0, "_rotation": 0.1}, "perturb_offset": true, "voxel_size": 1.5, "num_gaussians": 32, "2d_filter_kernel_size": 0.1, "3d_filter_kernel_size": 0.0009, "scaling_bias": 0.004, "opacity_bias": 0.1, "scaling_activation": "softplus"}}}