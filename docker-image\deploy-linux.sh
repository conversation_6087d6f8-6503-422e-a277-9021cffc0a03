#!/bin/bash

# TRELLIS Docker 一键部署脚本 (Linux/Ubuntu)

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${GREEN}$1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

echo
echo "========================================"
echo "   TRELLIS Docker 一键部署脚本 (Linux)"
echo "========================================"
echo

# 检查是否为root用户或在docker组中
if ! groups $USER | grep -q '\bdocker\b' && [ "$EUID" -ne 0 ]; then
    print_error "当前用户不在docker组中且不是root用户"
    print_info "请运行: sudo usermod -aG docker $USER"
    print_info "然后重新登录或运行: newgrp docker"
    exit 1
fi

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    print_error "Docker未安装"
    print_info "Ubuntu/Debian安装命令:"
    print_info "curl -fsSL https://get.docker.com -o get-docker.sh"
    print_info "sudo sh get-docker.sh"
    exit 1
fi

print_message "✅ Docker已安装"

# 检查Docker是否运行
if ! docker info &> /dev/null; then
    print_error "Docker服务未运行"
    print_info "启动Docker: sudo systemctl start docker"
    exit 1
fi

print_message "✅ Docker服务正在运行"

# 检查Docker Compose是否可用
if ! docker compose version &> /dev/null; then
    print_error "Docker Compose不可用"
    exit 1
fi

print_message "✅ Docker Compose可用"

# 检查镜像文件是否存在
if [ ! -f "trellis-inference.tar" ]; then
    print_error "推理镜像文件 trellis-inference.tar 不存在"
    exit 1
fi

if [ ! -f "trellis-training.tar" ]; then
    print_error "训练镜像文件 trellis-training.tar 不存在"
    exit 1
fi

print_message "✅ 镜像文件检查完成"

# 加载镜像函数
load_images() {
    echo
    print_info "🔄 加载Docker镜像..."
    print_info "正在加载推理镜像 (约45GB)..."
    docker load -i trellis-inference.tar
    
    print_info "正在加载训练镜像 (约45.7GB)..."
    docker load -i trellis-training.tar
    
    print_message "✅ 所有镜像加载完成"
    echo
    print_info "可用镜像:"
    docker images | grep trellis
    echo
}

# 检查镜像是否已加载
load_images_if_needed() {
    if ! docker images | grep -q "trellis.*latest"; then
        print_info "🔄 首次运行，正在加载镜像..."
        load_images
    fi
}

# 显示菜单
show_menu() {
    echo
    echo "请选择部署模式:"
    echo "1. 推理模式 (启动Web界面，端口7860)"
    echo "2. 训练模式 (启动训练环境)"
    echo "3. 完整模式 (同时启动推理和训练)"
    echo "4. 加载镜像 (仅加载Docker镜像)"
    echo "5. 清理环境 (停止并删除容器)"
    echo "6. 退出"
    echo
}

# 推理模式
start_inference() {
    echo
    print_info "🚀 启动推理模式..."
    load_images_if_needed
    docker compose -f docker-compose-deploy.yml up -d trellis-inference
    
    echo
    print_message "✅ TRELLIS推理服务已启动！"
    print_info "🌐 Web界面: http://localhost:7860"
    print_info "📊 状态检查: docker ps"
    print_info "📋 查看日志: docker logs trellis-inference"
    echo
    print_info "正在等待服务启动..."
    sleep 10
    
    # 尝试打开浏览器
    if command -v xdg-open &> /dev/null; then
        xdg-open http://localhost:7860 &> /dev/null &
    elif command -v firefox &> /dev/null; then
        firefox http://localhost:7860 &> /dev/null &
    elif command -v google-chrome &> /dev/null; then
        google-chrome http://localhost:7860 &> /dev/null &
    fi
}

# 训练模式
start_training() {
    echo
    print_info "🏋️ 启动训练模式..."
    load_images_if_needed
    docker compose -f docker-compose-deploy.yml up -d trellis-training
    
    echo
    print_message "✅ TRELLIS训练环境已启动！"
    print_info "🔧 进入容器: docker exec -it trellis-training bash"
    print_info "📊 状态检查: docker ps"
    print_info "📋 查看日志: docker logs trellis-training"
    echo
    print_info "训练命令示例:"
    print_info "  conda activate trellis"
    print_info "  python train.py --config configs/vae/slat_vae_enc_dec_gs_swin8_B_64l8_fp16.json"
}

# 完整模式
start_full() {
    echo
    print_info "🚀 启动完整模式..."
    load_images_if_needed
    docker compose -f docker-compose-deploy.yml up -d
    
    echo
    print_message "✅ TRELLIS完整环境已启动！"
    print_info "🌐 推理界面: http://localhost:7860"
    print_info "🏋️ 训练环境: docker exec -it trellis-training bash"
    print_info "📊 状态检查: docker ps"
    echo
    print_info "正在等待服务启动..."
    sleep 10
    
    # 尝试打开浏览器
    if command -v xdg-open &> /dev/null; then
        xdg-open http://localhost:7860 &> /dev/null &
    fi
}

# 清理环境
cleanup() {
    echo
    print_info "🧹 清理环境..."
    docker compose -f docker-compose-deploy.yml down
    docker system prune -f
    print_message "✅ 环境清理完成"
}

# 主循环
while true; do
    show_menu
    read -p "请输入选择 (1-6): " choice
    
    case $choice in
        1)
            start_inference
            ;;
        2)
            start_training
            ;;
        3)
            start_full
            ;;
        4)
            load_images
            ;;
        5)
            cleanup
            ;;
        6)
            print_message "👋 再见！"
            exit 0
            ;;
        *)
            print_error "无效选择，请输入1-6"
            ;;
    esac
    
    echo
    read -p "按Enter键继续..."
done
