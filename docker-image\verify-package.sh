#!/bin/bash

# TRELLIS Docker 部署包完整性验证脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_message() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

echo
echo "========================================"
echo "   TRELLIS 部署包完整性验证"
echo "========================================"
echo

# 验证文件完整性
print_info "验证核心文件..."

# 检查Docker镜像
if [ -f "trellis-inference.tar" ]; then
    size=$(du -h trellis-inference.tar | cut -f1)
    print_message "推理镜像: $size"
else
    print_error "推理镜像文件缺失"
    exit 1
fi

if [ -f "trellis-training.tar" ]; then
    size=$(du -h trellis-training.tar | cut -f1)
    print_message "训练镜像: $size"
else
    print_error "训练镜像文件缺失"
    exit 1
fi

# 检查模型文件夹
if [ -d "model" ]; then
    size=$(du -sh model | cut -f1)
    model_count=$(find model -name "*.pt" | wc -l)
    print_message "预训练模型: $size ($model_count 个模型文件)"
    
    # 检查具体模型
    models=("JeffreyXiang_TRELLIS-image-large" "JeffreyXiang_TRELLIS-text-base" "JeffreyXiang_TRELLIS-text-large" "JeffreyXiang_TRELLIS-text-xlarge")
    for model in "${models[@]}"; do
        if [ -d "model/$model" ]; then
            ckpts=$(find "model/$model" -name "*.pt" | wc -l)
            print_message "  $model ($ckpts 检查点)"
        else
            print_warning "  $model 缺失"
        fi
    done
else
    print_error "模型文件夹缺失"
    exit 1
fi

# 检查配置文件夹
if [ -d "configs" ]; then
    size=$(du -sh configs | cut -f1)
    config_count=$(find configs -name "*.json" | wc -l)
    print_message "训练配置: $size ($config_count 个配置文件)"
else
    print_error "配置文件夹缺失"
    exit 1
fi

# 检查部署脚本
scripts=("deploy-windows.bat" "deploy-linux.sh" "docker-compose-deploy.yml")
for script in "${scripts[@]}"; do
    if [ -f "$script" ]; then
        print_message "部署脚本: $script"
    else
        print_error "部署脚本缺失: $script"
        exit 1
    fi
done

# 检查工作目录
dirs=("outputs" "datasets" "logs" "tmp")
for dir in "${dirs[@]}"; do
    if [ -d "$dir" ]; then
        print_message "工作目录: $dir/"
    else
        print_warning "工作目录缺失: $dir/ (将自动创建)"
        mkdir -p "$dir"
    fi
done

echo
print_info "验证Docker镜像完整性..."

# 验证推理镜像
if command -v docker &> /dev/null; then
    print_info "测试推理镜像加载..."
    if docker load --input trellis-inference.tar --quiet; then
        print_message "推理镜像验证成功"
    else
        print_error "推理镜像损坏"
        exit 1
    fi
    
    print_info "测试训练镜像加载..."
    if docker load --input trellis-training.tar --quiet; then
        print_message "训练镜像验证成功"
    else
        print_error "训练镜像损坏"
        exit 1
    fi
    
    print_info "验证Docker Compose配置..."
    if docker compose -f docker-compose-deploy.yml config > /dev/null 2>&1; then
        print_message "Docker Compose配置有效"
    else
        print_error "Docker Compose配置无效"
        exit 1
    fi
    
    print_info "检查加载的镜像..."
    docker images | grep trellis
else
    print_warning "Docker未安装，跳过镜像验证"
fi

echo
print_info "计算部署包总大小..."
total_size=$(du -sh . | cut -f1)
print_message "部署包总大小: $total_size"

echo
print_message "🎉 部署包验证完成！"
print_info "包含内容:"
print_info "  📦 Docker镜像: 2个 (~31GB)"
print_info "  🤖 预训练模型: 4个 (~9.7GB)"
print_info "  ⚙️  训练配置: $(find configs -name "*.json" | wc -l)个"
print_info "  📁 工作目录: 4个"
print_info "  🚀 部署脚本: 完整"

echo
print_info "现在可以开始部署:"
print_info "  Windows: deploy-windows.bat"
print_info "  Linux:   ./deploy-linux.sh"

echo
