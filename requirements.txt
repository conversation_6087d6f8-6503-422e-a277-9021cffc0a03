# TRELLIS Requirements
# This file lists the main dependencies for TRELLIS
# Note: Some packages require specific installation methods (see setup.sh)

# Core dependencies
torch==2.4.0
torchvision==0.19.0

# Basic Python packages
pillow
imageio
imageio-ffmpeg
tqdm
easydict
opencv-python-headless
scipy
ninja
rembg
onnxruntime
trimesh
open3d
xatlas
pyvista
pymeshfix
igraph
transformers

# Gradio for web interface
gradio==4.44.1
gradio_litmodel3d==0.0.1

# Training dependencies (optional)
tensorboard
pandas
lpips
pillow-simd

# GPU-accelerated packages (require special installation)
# These are installed via specific methods in setup.sh:
# - xformers (version depends on PyTorch/CUDA)
# - flash-attn
# - spconv-cu118 or spconv-cu120
# - kaolin (from NVIDIA)
# - nvdiffrast (from source)
# - diffoctreerast (from source)
# - mip-splatting (from source)
# - vox2seq (from local extensions)

# Utils3d from git
# git+https://github.com/EasternJournalist/utils3d.git@9a4eb15e4021b67b12c460c7057d642626897ec8
