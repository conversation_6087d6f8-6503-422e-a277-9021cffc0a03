@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    TRELLIS Docker 部署包测试 (Windows)
echo ========================================
echo.

:: 检查文件完整性
echo ℹ️  检查部署包文件...

set "files=trellis-inference.tar trellis-training.tar deploy-windows.bat deploy-linux.sh docker-compose-deploy.yml README.md"

for %%f in (%files%) do (
    if exist "%%f" (
        for %%s in ("%%f") do (
            echo ✅ %%f ^(%%~zs bytes^)
        )
    ) else (
        echo ❌ %%f 不存在
        pause
        exit /b 1
    )
)

echo.
echo ℹ️  检查Docker是否可用...
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker未安装或未启动
    pause
    exit /b 1
)
echo ✅ Docker可用

echo.
echo ℹ️  检查Docker Compose配置...
docker compose -f docker-compose-deploy.yml config >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker Compose配置无效
    pause
    exit /b 1
)
echo ✅ Docker Compose配置有效

echo.
echo ℹ️  测试镜像文件完整性...
echo 正在测试推理镜像...
docker load -i trellis-inference.tar >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 推理镜像损坏或格式错误
    pause
    exit /b 1
)
echo ✅ 推理镜像测试成功

echo 正在测试训练镜像...
docker load -i trellis-training.tar >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 训练镜像损坏或格式错误
    pause
    exit /b 1
)
echo ✅ 训练镜像测试成功

echo.
echo ℹ️  检查镜像信息...
docker images | findstr trellis

echo.
echo 🎉 部署包测试完成！所有文件完整且有效。
echo ℹ️  现在可以使用部署脚本进行安装：
echo    Windows: deploy-windows.bat
echo    Linux: ./deploy-linux.sh
echo.
pause
