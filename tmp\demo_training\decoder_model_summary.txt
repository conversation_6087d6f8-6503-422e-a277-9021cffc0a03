Parameters:
================================================================================================================================
Name                                                                    Shape                           Type            Grad
input_layer.weight                                                      torch.Size([256, 8])            torch.float32   True
input_layer.bias                                                        torch.Size([256])               torch.float32   True
blocks.0.attn.to_qkv.weight                                             torch.Size([768, 256])          torch.float16   True
blocks.0.attn.to_qkv.bias                                               torch.Size([768])               torch.float16   True
blocks.0.attn.to_out.weight                                             torch.Size([256, 256])          torch.float16   True
blocks.0.attn.to_out.bias                                               torch.Size([256])               torch.float16   True
blocks.0.mlp.mlp.0.weight                                               torch.Size([1024, 256])         torch.float16   True
blocks.0.mlp.mlp.0.bias                                                 torch.Size([1024])              torch.float16   True
blocks.0.mlp.mlp.2.weight                                               torch.Size([256, 1024])         torch.float16   True
blocks.0.mlp.mlp.2.bias                                                 torch.Size([256])               torch.float16   True
blocks.1.attn.to_qkv.weight                                             torch.Size([768, 256])          torch.float16   True
blocks.1.attn.to_qkv.bias                                               torch.Size([768])               torch.float16   True
blocks.1.attn.to_out.weight                                             torch.Size([256, 256])          torch.float16   True
blocks.1.attn.to_out.bias                                               torch.Size([256])               torch.float16   True
blocks.1.mlp.mlp.0.weight                                               torch.Size([1024, 256])         torch.float16   True
blocks.1.mlp.mlp.0.bias                                                 torch.Size([1024])              torch.float16   True
blocks.1.mlp.mlp.2.weight                                               torch.Size([256, 1024])         torch.float16   True
blocks.1.mlp.mlp.2.bias                                                 torch.Size([256])               torch.float16   True
blocks.2.attn.to_qkv.weight                                             torch.Size([768, 256])          torch.float16   True
blocks.2.attn.to_qkv.bias                                               torch.Size([768])               torch.float16   True
blocks.2.attn.to_out.weight                                             torch.Size([256, 256])          torch.float16   True
blocks.2.attn.to_out.bias                                               torch.Size([256])               torch.float16   True
blocks.2.mlp.mlp.0.weight                                               torch.Size([1024, 256])         torch.float16   True
blocks.2.mlp.mlp.0.bias                                                 torch.Size([1024])              torch.float16   True
blocks.2.mlp.mlp.2.weight                                               torch.Size([256, 1024])         torch.float16   True
blocks.2.mlp.mlp.2.bias                                                 torch.Size([256])               torch.float16   True
blocks.3.attn.to_qkv.weight                                             torch.Size([768, 256])          torch.float16   True
blocks.3.attn.to_qkv.bias                                               torch.Size([768])               torch.float16   True
blocks.3.attn.to_out.weight                                             torch.Size([256, 256])          torch.float16   True
blocks.3.attn.to_out.bias                                               torch.Size([256])               torch.float16   True
blocks.3.mlp.mlp.0.weight                                               torch.Size([1024, 256])         torch.float16   True
blocks.3.mlp.mlp.0.bias                                                 torch.Size([1024])              torch.float16   True
blocks.3.mlp.mlp.2.weight                                               torch.Size([256, 1024])         torch.float16   True
blocks.3.mlp.mlp.2.bias                                                 torch.Size([256])               torch.float16   True
out_layer.weight                                                        torch.Size([224, 256])          torch.float32   True
out_layer.bias                                                          torch.Size([224])               torch.float32   True

Number of parameters: 3214816
Number of trainable parameters: 3214816

