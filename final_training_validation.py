#!/usr/bin/env python3
"""
TRELLIS 最终训练验证脚本
完整验证训练环境，包括实际的训练步骤
"""

import os
import sys
import json
import torch
import numpy as np
import random
from pathlib import Path
from trellis import models, datasets, trainers

# 设置环境变量
os.environ['SPCONV_ALGO'] = 'native'
os.environ['ATTN_BACKEND'] = 'xformers'

def setup_rng(seed=42):
    """设置随机种子"""
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    random.seed(seed)

def create_simple_config():
    """创建简化的训练配置"""
    config = {
        "models": {
            "encoder": {
                "name": "SLatEncoder",
                "args": {
                    "resolution": 32,  # 进一步减小
                    "in_channels": 512,  # 减小输入通道
                    "model_channels": 128,  # 减小模型通道
                    "latent_channels": 4,  # 减小潜在通道
                    "num_blocks": 2,  # 减少块数
                    "num_heads": 4,
                    "mlp_ratio": 2,
                    "attn_mode": "swin",
                    "window_size": 4,
                    "use_fp16": True
                }
            },
            "decoder": {
                "name": "SLatGaussianDecoder",
                "args": {
                    "resolution": 32,
                    "model_channels": 128,
                    "latent_channels": 4,
                    "num_blocks": 2,
                    "num_heads": 4,
                    "mlp_ratio": 2,
                    "attn_mode": "swin",
                    "window_size": 4,
                    "use_fp16": True,
                    "representation_config": {
                        "lr": {
                            "_xyz": 1.0,
                            "_features_dc": 1.0,
                            "_opacity": 1.0,
                            "_scaling": 1.0,
                            "_rotation": 0.1
                        },
                        "perturb_offset": True,
                        "voxel_size": 1.5,
                        "num_gaussians": 8,  # 进一步减少
                        "2d_filter_kernel_size": 0.1,
                        "3d_filter_kernel_size": 9e-4,
                        "scaling_bias": 4e-3,
                        "opacity_bias": 0.1,
                        "scaling_activation": "softplus"
                    }
                }
            }
        },
        "trainer": {
            "name": "SLatVaeGaussianTrainer",
            "args": {
                "max_steps": 5,  # 只训练5步
                "batch_size_per_gpu": 1,
                "batch_split": 1,
                "optimizer": {
                    "name": "AdamW",
                    "args": {
                        "lr": 1e-4,
                        "weight_decay": 0.0
                    }
                },
                "ema_rate": [0.9999],
                "fp16_mode": "inflat_all",
                "fp16_scale_growth": 0.001,
                "grad_clip": 1.0,
                "i_log": 1,
                "i_sample": 10,
                "i_save": 10,
                "loss_type": "l1",
                "lambda_ssim": 0.0,  # 禁用SSIM损失
                "lambda_lpips": 0.0,  # 禁用LPIPS损失
                "lambda_kl": 1e-6,
                "regularizations": {
                    "lambda_vol": 100.0,
                    "lambda_opacity": 0.001
                }
            }
        }
    }
    return config

class MinimalDataset:
    """最小化数据集用于训练验证"""
    def __init__(self):
        self.value_range = (-1, 1)
        
    def __len__(self):
        return 10  # 非常小的数据集
        
    def __getitem__(self, idx):
        # 创建符合训练器期望的数据格式
        batch_size = 1
        resolution = 32
        num_voxels = 100
        
        # 创建稀疏坐标 (batch_idx, x, y, z)
        coords = torch.randint(0, resolution, (num_voxels, 3))
        batch_coords = torch.cat([
            torch.zeros(num_voxels, 1, dtype=torch.long),  # batch index
            coords
        ], dim=1)
        
        # 创建稀疏特征
        features = torch.randn(num_voxels, 512)
        
        # 创建渲染图像
        images = torch.randn(6, 3, 128, 128)  # 6个视角
        
        # 创建相机参数
        cameras = torch.randn(6, 16)
        
        return {
            'sparse_structure_coords': batch_coords,
            'sparse_structure_feats': features,
            'images': images,
            'cameras': cameras,
        }
    
    def collate_fn(self, batch):
        """批处理函数"""
        # 合并所有数据
        all_coords = []
        all_feats = []
        all_images = []
        all_cameras = []
        
        for i, item in enumerate(batch):
            # 更新批次索引
            coords = item['sparse_structure_coords'].clone()
            coords[:, 0] = i
            all_coords.append(coords)
            all_feats.append(item['sparse_structure_feats'])
            all_images.append(item['images'])
            all_cameras.append(item['cameras'])
        
        return {
            'sparse_structure_coords': torch.cat(all_coords, dim=0),
            'sparse_structure_feats': torch.cat(all_feats, dim=0),
            'images': torch.stack(all_images),
            'cameras': torch.stack(all_cameras),
        }

def test_complete_training():
    """测试完整的训练流程"""
    print("🚀 TRELLIS 完整训练验证")
    print("=" * 60)
    
    # 设置随机种子
    setup_rng(42)
    
    # 创建输出目录
    output_dir = "/app/tmp/final_training_validation"
    os.makedirs(output_dir, exist_ok=True)
    
    # 创建配置
    config = create_simple_config()
    
    # 保存配置
    with open(os.path.join(output_dir, "config.json"), "w") as f:
        json.dump(config, f, indent=2)
    
    print("🔧 创建模型...")
    
    # 创建模型
    model_dict = {}
    for name, model_config in config["models"].items():
        print(f"  创建模型: {name}")
        model = getattr(models, model_config["name"])(**model_config["args"])
        model = model.cuda()
        model_dict[name] = model
        
        # 计算参数数量
        num_params = sum(p.numel() for p in model.parameters())
        print(f"    参数数量: {num_params:,}")
    
    print("📊 创建数据集...")
    dataset = MinimalDataset()
    print(f"  数据集大小: {len(dataset)}")
    
    print("🏋️ 创建训练器...")
    try:
        trainer = getattr(trainers, config["trainer"]["name"])(
            model_dict,
            dataset,
            output_dir=output_dir,
            load_dir=None,
            step=None,
            **config["trainer"]["args"]
        )
        print("✅ 训练器创建成功")
        
        print("🎯 开始训练...")
        
        # 运行几个训练步骤
        trainer.run()
        
        print("✅ 训练完成！")
        
        # 检查输出文件
        print("\n📁 检查输出文件:")
        for file in os.listdir(output_dir):
            file_path = os.path.join(output_dir, file)
            if os.path.isfile(file_path):
                size = os.path.getsize(file_path)
                print(f"  {file}: {size} bytes")
        
        return True
        
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    success = test_complete_training()
    
    if success:
        print("\n🎉 训练验证成功！")
        print("✅ TRELLIS训练环境完全正常工作")
        print("\n📝 现在可以运行真实训练:")
        print("1. 准备数据集")
        print("2. 选择配置文件")
        print("3. 运行训练命令:")
        print("   python train.py --config configs/vae/slat_vae_enc_dec_gs_swin8_B_64l8_fp16.json \\")
        print("                   --output_dir outputs/my_training \\")
        print("                   --data_dir /path/to/dataset")
    else:
        print("\n❌ 训练验证失败")
        print("请检查错误信息并修复问题")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
