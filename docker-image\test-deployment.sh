#!/bin/bash

# TRELLIS Docker 部署包测试脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_message() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

echo
echo "========================================"
echo "   TRELLIS Docker 部署包测试"
echo "========================================"
echo

# 检查文件完整性
print_info "检查部署包文件..."

required_files=(
    "trellis-inference.tar"
    "trellis-training.tar"
    "deploy-windows.bat"
    "deploy-linux.sh"
    "docker-compose-deploy.yml"
    "README.md"
)

for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        size=$(du -h "$file" | cut -f1)
        print_message "$file ($size)"
    else
        print_error "$file 不存在"
        exit 1
    fi
done

echo
print_info "检查Docker镜像文件完整性..."

# 检查推理镜像
if docker load --input trellis-inference.tar --quiet; then
    print_message "推理镜像加载测试成功"
else
    print_error "推理镜像损坏或格式错误"
    exit 1
fi

# 检查训练镜像
if docker load --input trellis-training.tar --quiet; then
    print_message "训练镜像加载测试成功"
else
    print_error "训练镜像损坏或格式错误"
    exit 1
fi

echo
print_info "检查镜像信息..."
docker images | grep trellis

echo
print_info "检查Docker Compose配置..."
if docker compose -f docker-compose-deploy.yml config > /dev/null 2>&1; then
    print_message "Docker Compose配置有效"
else
    print_error "Docker Compose配置无效"
    exit 1
fi

echo
print_message "🎉 部署包测试完成！所有文件完整且有效。"
print_info "现在可以使用部署脚本进行安装："
print_info "Windows: deploy-windows.bat"
print_info "Linux: ./deploy-linux.sh"

echo
