#!/bin/bash

# TRELLIS Docker Runner Script
# This script helps you easily run TRELLIS in Docker

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Docker is installed and running
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi
    
    print_success "Docker is available and running."
}

# Function to check if Docker Compose is available
check_docker_compose() {
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        print_error "Docker Compose is not available. Please install Docker Compose."
        exit 1
    fi
    
    print_success "Docker Compose is available."
}

# Function to check NVIDIA Docker runtime
check_nvidia_docker() {
    if ! docker run --rm --gpus all nvidia/cuda:11.8-base-ubuntu20.04 nvidia-smi &> /dev/null; then
        print_warning "NVIDIA Docker runtime may not be properly configured."
        print_warning "Please make sure you have NVIDIA Docker installed and configured."
        print_warning "Visit: https://docs.nvidia.com/datacenter/cloud-native/container-toolkit/install-guide.html"
    else
        print_success "NVIDIA Docker runtime is working."
    fi
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  build       Build the Docker image"
    echo "  run         Run the main TRELLIS demo (image-to-3D)"
    echo "  run-text    Run the text-to-3D demo"
    echo "  example     Run the example script"
    echo "  shell       Open a shell in the container"
    echo "  stop        Stop all running containers"
    echo "  clean       Remove containers and images"
    echo "  logs        Show container logs"
    echo ""
    echo "Options:"
    echo "  --gpu ID    Specify GPU ID to use (default: 0)"
    echo "  --port PORT Specify port for web interface (default: 7860)"
    echo "  --help      Show this help message"
}

# Default values
GPU_ID="0"
PORT="7860"
COMMAND=""

# Parse arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        build|run|run-text|example|shell|stop|clean|logs)
            COMMAND="$1"
            shift
            ;;
        --gpu)
            GPU_ID="$2"
            shift 2
            ;;
        --port)
            PORT="$2"
            shift 2
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Check if command is provided
if [[ -z "$COMMAND" ]]; then
    print_error "No command specified."
    show_usage
    exit 1
fi

# Set environment variables
export CUDA_VISIBLE_DEVICES="$GPU_ID"

# Execute commands
case $COMMAND in
    build)
        print_info "Building TRELLIS Docker image..."
        check_docker
        check_docker_compose
        docker-compose build
        print_success "Docker image built successfully!"
        ;;
    
    run)
        print_info "Starting TRELLIS demo (image-to-3D)..."
        check_docker
        check_docker_compose
        check_nvidia_docker
        
        # Create necessary directories
        mkdir -p outputs tmp
        
        # Set port in environment
        export TRELLIS_PORT="$PORT"
        
        print_info "Starting container on port $PORT..."
        print_info "GPU ID: $GPU_ID"
        
        docker-compose up trellis
        ;;
    
    run-text)
        print_info "Starting TRELLIS text-to-3D demo..."
        check_docker
        check_docker_compose
        check_nvidia_docker
        
        mkdir -p outputs tmp
        export TRELLIS_PORT="$PORT"
        
        print_info "Starting text-to-3D container on port $PORT..."
        print_info "GPU ID: $GPU_ID"
        
        docker-compose --profile text up trellis-text
        ;;
    
    example)
        print_info "Running TRELLIS example..."
        check_docker
        check_docker_compose
        check_nvidia_docker
        
        mkdir -p outputs
        
        docker-compose --profile example up trellis-example
        ;;
    
    shell)
        print_info "Opening shell in TRELLIS container..."
        check_docker
        
        docker run -it --rm --gpus all \
            -v "$(pwd):/app" \
            -v "$(pwd)/outputs:/app/outputs" \
            -e CUDA_VISIBLE_DEVICES="$GPU_ID" \
            -e ATTN_BACKEND=flash-attn \
            -e SPCONV_ALGO=native \
            trellis:latest \
            conda run -n trellis bash
        ;;
    
    stop)
        print_info "Stopping all TRELLIS containers..."
        check_docker_compose
        docker-compose down
        print_success "All containers stopped."
        ;;
    
    clean)
        print_info "Cleaning up TRELLIS containers and images..."
        check_docker_compose
        docker-compose down --rmi all --volumes --remove-orphans
        print_success "Cleanup completed."
        ;;
    
    logs)
        print_info "Showing container logs..."
        check_docker_compose
        docker-compose logs -f
        ;;
    
    *)
        print_error "Unknown command: $COMMAND"
        show_usage
        exit 1
        ;;
esac
