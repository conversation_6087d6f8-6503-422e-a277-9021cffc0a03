#!/usr/bin/env python3
"""
Test script for TRELLIS training functionality
"""

import sys
import os
import torch
import json
from pathlib import Path

def test_config_loading():
    """Test loading training configurations"""
    print("Testing configuration loading...")
    
    config_dir = Path("/app/configs")
    if not config_dir.exists():
        print(f"❌ Config directory not found: {config_dir}")
        return False
    
    # Test loading a sample config
    sample_configs = [
        "configs/generation/slat_flow_img_dit_L_64l8p2_fp16.json",
        "configs/vae/slat_vae_enc_dec_gs_swin8_B_64l8_fp16.json"
    ]
    
    for config_path in sample_configs:
        if os.path.exists(config_path):
            try:
                with open(config_path, 'r') as f:
                    config = json.load(f)
                print(f"✅ Loaded config: {config_path}")
                print(f"   Model: {config.get('model', {}).get('name', 'Unknown')}")
                print(f"   Trainer: {config.get('trainer', {}).get('name', 'Unknown')}")
            except Exception as e:
                print(f"❌ Failed to load config {config_path}: {e}")
                return False
        else:
            print(f"⚠️  Config not found: {config_path}")
    
    return True

def test_model_creation():
    """Test creating TRELLIS models"""
    print("\nTesting model creation...")

    try:
        from trellis.models import SLatEncoder, SLatGaussianDecoder, SLatFlowModel

        # Test creating a simple encoder with correct parameters
        encoder = SLatEncoder(
            resolution=64,
            in_channels=4,
            model_channels=256,
            latent_channels=32,
            num_blocks=4,
            num_heads=8,
            attn_mode="swin",
            window_size=8
        )
        print("✅ SLatEncoder created successfully")

        # Test creating a decoder with correct parameters
        decoder = SLatGaussianDecoder(
            resolution=64,
            model_channels=256,
            latent_channels=32,
            num_blocks=4,
            num_heads=8,
            attn_mode="swin",
            window_size=8,
            representation_config={
                'num_gaussians': 1000,
                'voxel_size': 0.1,
                '3d_filter_kernel_size': 0.1,
                'scaling_bias': 0.01,
                'opacity_bias': 0.01,
                'scaling_activation': 'exp',
                'lr': {'_xyz': 1.0, '_features_dc': 1.0, '_scaling': 1.0, '_rotation': 1.0, '_opacity': 1.0},
                'perturb_offset': True
            }
        )
        print("✅ SLatGaussianDecoder created successfully")

        # Test creating a flow model with correct parameters
        flow_model = SLatFlowModel(
            resolution=64,
            in_channels=32,
            model_channels=256,
            cond_channels=512,
            out_channels=32,
            num_blocks=12,
            num_heads=16,
            patch_size=2
        )
        print("✅ SLatFlowModel created successfully")

        return True
    except Exception as e:
        print(f"❌ Model creation failed: {e}")
        return False

def test_trainer_creation():
    """Test creating TRELLIS trainer"""
    print("\nTesting trainer creation...")

    try:
        from trellis.trainers import BasicTrainer

        # Create dummy models and dataset for trainer
        class DummyModel(torch.nn.Module):
            def __init__(self):
                super().__init__()
                self.linear = torch.nn.Linear(10, 10)

            def forward(self, x):
                return self.linear(x)

        class DummyDataset:
            def __init__(self):
                self.value_range = (-1, 1)

            def __len__(self):
                return 100

            def __getitem__(self, idx):
                return {'data': torch.randn(10)}

        class TestBasicTrainer(BasicTrainer):
            def training_losses(self, **mb_data):
                # Simple dummy loss
                loss = torch.tensor(1.0, requires_grad=True)
                status = {'dummy_status': 1.0}
                return {'loss': loss}, status

            def run_snapshot(self, num_samples, batch_size=4, verbose=False, **kwargs):
                # Simple dummy snapshot
                return {'sample': {'value': torch.randn(num_samples, 3, 64, 64), 'type': 'sample'}}

        models = {'model': DummyModel()}
        dataset = DummyDataset()

        # Create a simple trainer configuration with required parameters
        trainer = TestBasicTrainer(
            models=models,
            dataset=dataset,
            output_dir="/app/tmp/test_training",
            load_dir=None,
            step=None,
            max_steps=100,
            batch_size_per_gpu=4,
            optimizer={'name': 'Adam', 'args': {'lr': 1e-4}},
            i_print=10,
            i_log=10,
            i_sample=50,
            i_save=50
        )
        print("✅ BasicTrainer created successfully")

        return True
    except Exception as e:
        print(f"❌ Trainer creation failed: {e}")
        return False

def test_pipeline_loading():
    """Test loading pretrained pipelines"""
    print("\nTesting pipeline loading...")
    
    try:
        from trellis.pipelines import TrellisImageTo3DPipeline
        
        # Test if we can create a pipeline (without loading weights)
        print("✅ TrellisImageTo3DPipeline class available")
        
        # Check if model files exist for pipeline loading
        model_paths = [
            "/app/model/JeffreyXiang_TRELLIS-image-large",
            "/app/model/JeffreyXiang_TRELLIS-text-large"
        ]
        
        for model_path in model_paths:
            if os.path.exists(model_path):
                pipeline_config = os.path.join(model_path, "pipeline.json")
                if os.path.exists(pipeline_config):
                    print(f"✅ Pipeline config found: {model_path}")
                else:
                    print(f"⚠️  Pipeline config missing: {pipeline_config}")
            else:
                print(f"❌ Model path not found: {model_path}")
        
        return True
    except Exception as e:
        print(f"❌ Pipeline test failed: {e}")
        return False

def test_cuda_memory():
    """Test CUDA memory allocation"""
    print("\nTesting CUDA memory...")
    
    if not torch.cuda.is_available():
        print("⚠️  CUDA not available, skipping memory test")
        return True
    
    try:
        device = torch.device('cuda:0')
        
        # Test basic tensor operations
        x = torch.randn(1000, 1000, device=device)
        y = torch.randn(1000, 1000, device=device)
        z = torch.matmul(x, y)
        
        print(f"✅ CUDA tensor operations successful")
        print(f"   Memory allocated: {torch.cuda.memory_allocated(device) / 1024**2:.1f} MB")
        print(f"   Memory reserved: {torch.cuda.memory_reserved(device) / 1024**2:.1f} MB")
        
        # Clean up
        del x, y, z
        torch.cuda.empty_cache()
        
        return True
    except Exception as e:
        print(f"❌ CUDA memory test failed: {e}")
        return False

def test_training_script():
    """Test if training script exists and is executable"""
    print("\nTesting training script...")
    
    train_script = "/app/train.py"
    if os.path.exists(train_script):
        print(f"✅ Training script found: {train_script}")
        
        # Test if we can import the training script
        try:
            import importlib.util
            spec = importlib.util.spec_from_file_location("train", train_script)
            train_module = importlib.util.module_from_spec(spec)
            # Don't execute, just check if it can be loaded
            print("✅ Training script can be imported")
        except Exception as e:
            print(f"⚠️  Training script import issue: {e}")
        
        return True
    else:
        print(f"❌ Training script not found: {train_script}")
        return False

def main():
    """Main test function"""
    print("🚀 TRELLIS Training Functionality Test")
    print("=" * 50)
    
    tests = [
        test_config_loading,
        test_model_creation,
        test_trainer_creation,
        test_pipeline_loading,
        test_cuda_memory,
        test_training_script
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    print("📊 Training Test Summary")
    print("=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"✅ All training tests passed ({passed}/{total})")
        print("🎉 Training functionality is ready!")
        return 0
    else:
        print(f"❌ Some training tests failed ({passed}/{total})")
        print("⚠️  Please check the issues above")
        return 1

if __name__ == "__main__":
    sys.exit(main())
