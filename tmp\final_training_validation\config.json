{"models": {"encoder": {"name": "SLatEncoder", "args": {"resolution": 32, "in_channels": 512, "model_channels": 128, "latent_channels": 4, "num_blocks": 2, "num_heads": 4, "mlp_ratio": 2, "attn_mode": "swin", "window_size": 4, "use_fp16": true}}, "decoder": {"name": "SLatGaussianDecoder", "args": {"resolution": 32, "model_channels": 128, "latent_channels": 4, "num_blocks": 2, "num_heads": 4, "mlp_ratio": 2, "attn_mode": "swin", "window_size": 4, "use_fp16": true, "representation_config": {"lr": {"_xyz": 1.0, "_features_dc": 1.0, "_opacity": 1.0, "_scaling": 1.0, "_rotation": 0.1}, "perturb_offset": true, "voxel_size": 1.5, "num_gaussians": 8, "2d_filter_kernel_size": 0.1, "3d_filter_kernel_size": 0.0009, "scaling_bias": 0.004, "opacity_bias": 0.1, "scaling_activation": "softplus"}}}}, "trainer": {"name": "SLatVaeGaussianTrainer", "args": {"max_steps": 5, "batch_size_per_gpu": 1, "batch_split": 1, "optimizer": {"name": "AdamW", "args": {"lr": 0.0001, "weight_decay": 0.0}}, "ema_rate": [0.9999], "fp16_mode": "inflat_all", "fp16_scale_growth": 0.001, "grad_clip": 1.0, "i_log": 1, "i_sample": 10, "i_save": 10, "loss_type": "l1", "lambda_ssim": 0.0, "lambda_lpips": 0.0, "lambda_kl": 1e-06, "regularizations": {"lambda_vol": 100.0, "lambda_opacity": 0.001}}}}